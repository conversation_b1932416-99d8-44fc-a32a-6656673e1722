# Project Integration

这是一个使用Maven管理的多模块Java项目，用于数据集成。

## 项目结构

```
project-integration/
├── pom.xml                    # 根POM文件
├── core/                      # 核心模块
│   ├── pom.xml               # 核心模块POM
│   ├── engine/               # 核心引擎模块
│   │   ├── pom.xml
│   │   └── src/main/java/com/example/core/engine/
│   └── manager/              # 核心管理模块
│       ├── pom.xml
│       └── src/main/java/com/example/core/manager/
└── plugins/                   # 插件模块
    ├── pom.xml               # 插件模块POM
    └── connector/            # 连接器插件
        ├── pom.xml           # 连接器POM
        ├── common/           # 通用连接器
        │   ├── pom.xml
        │   └── src/main/java/com/example/plugins/connector/common/
        ├── mysql/            # MySQL连接器
        │   ├── pom.xml
        │   └── src/main/java/com/example/plugins/connector/mysql/
        ├── mongodb/          # MongoDB连接器
        │   ├── pom.xml
        │   └── src/main/java/com/example/plugins/connector/mongodb/
        └── oracle/           # Oracle连接器
            ├── pom.xml
            └── src/main/java/com/example/plugins/connector/oracle/
```

## 模块说明

### 核心模块 (core)
- **core-engine**: 核心处理引擎，提供基础的数据处理功能
- **core-manager**: 核心管理功能，依赖于core-engine

### 插件模块 (plugins)
- **connector-common**: 数据库连接器的通用接口和基础功能
- **connector-mysql**: MySQL数据库连接器实现
- **connector-mongodb**: MongoDB数据库连接器实现  
- **connector-oracle**: Oracle数据库连接器实现

## 技术栈

- **Java**: 21
- **Maven**: 3.x
- **JUnit**: 5.9.2
- **SLF4J**: 2.0.7 + Logback 1.4.7
- **数据库驱动**:
  - MySQL Connector/J: 8.0.33
  - MongoDB Driver: 4.9.1
  - Oracle JDBC: 21.9.0.0

## 构建和运行

### 前置条件
确保已安装Java 21和Maven 3.x。

### 编译项目
```bash
# 切换到JDK 21
source ~/app/java/jdk21.sh

# 编译所有模块
mvn compile
```

### 运行测试
```bash
mvn test
```

### 打包项目
```bash
mvn package
```

### 清理项目
```bash
mvn clean
```

## 依赖关系

- `core-manager` 依赖 `core-engine`
- `connector-mysql/mongodb/oracle` 依赖 `connector-common`
- `connector-common` 依赖 `core-engine`

## 开发指南

1. 所有模块都遵循标准的Maven目录结构
2. 使用SLF4J进行日志记录
3. 使用JUnit 5进行单元测试
4. 遵循Java 21的语言特性和最佳实践
