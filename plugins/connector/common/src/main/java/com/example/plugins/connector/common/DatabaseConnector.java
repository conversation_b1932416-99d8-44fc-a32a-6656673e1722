package com.example.plugins.connector.common;

/**
 * Base interface for database connectors
 */
public interface DatabaseConnector {
    
    /**
     * Connect to the database
     */
    void connect();
    
    /**
     * Disconnect from the database
     */
    void disconnect();
    
    /**
     * Test the connection
     * @return true if connection is valid
     */
    boolean testConnection();
}
